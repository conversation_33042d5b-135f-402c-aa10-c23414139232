import { vi } from 'vitest'

// Mock wagmi hooks
export const mockWriteContractAsync = vi.fn()
export const mockUseWriteContract = vi.fn(() => ({
  writeContractAsync: mockWriteContractAsync,
  isPending: false,
  isSuccess: false,
  error: null,
}))

export const mockGetPackageDetails = vi.fn()
export const mockUsePackageManager = vi.fn(() => ({
  packageCount: 3,
  getPackageDetails: mockGetPackageDetails,
}))

// Mock contract configuration
export const mockGetPackageManagerContract = vi.fn(() => ({
  address: '0x1234567890123456789012345678901234567890',
  abi: [],
}))

// Mock viem utilities
export const mockParseUnits = vi.fn((value: string, decimals: number) => BigInt(value) * BigInt(10 ** decimals))
export const mockFormatUnits = vi.fn((value: bigint, decimals: number) => (Number(value) / (10 ** decimals)).toString())

// Setup default mock implementations
beforeEach(() => {
  // Reset all mocks
  vi.clearAllMocks()
  
  // Setup default successful responses
  mockWriteContractAsync.mockResolvedValue({ hash: '0xmockhash' })
  
  mockGetPackageDetails.mockImplementation((id: number) => Promise.resolve({
    entryUSDT: BigInt('1000000000000000000000'), // 1000 USDT
    priceBps: 1200, // 12% APY
    vestSplitBps: 5000, // 50% vesting
    referralRateBps: 500, // 5% referral
    active: true,
    exists: true,
  }))
  
  mockUseWriteContract.mockReturnValue({
    writeContractAsync: mockWriteContractAsync,
    isPending: false,
    isSuccess: false,
    error: null,
  })
  
  mockUsePackageManager.mockReturnValue({
    packageCount: 3,
    getPackageDetails: mockGetPackageDetails,
  })
  
  mockGetPackageManagerContract.mockReturnValue({
    address: '0x1234567890123456789012345678901234567890',
    abi: [],
  })
})

// Export mock modules
vi.mock('wagmi', () => ({
  useWriteContract: mockUseWriteContract,
}))

vi.mock('../hooks/usePackageManager', () => ({
  usePackageManager: mockUsePackageManager,
}))

vi.mock('../contracts', () => ({
  getPackageManagerContract: mockGetPackageManagerContract,
}))

vi.mock('viem', () => ({
  parseUnits: mockParseUnits,
  formatUnits: mockFormatUnits,
}))
