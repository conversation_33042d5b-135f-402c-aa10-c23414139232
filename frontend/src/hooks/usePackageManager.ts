import { useAccount, useReadContract, useWriteContract } from 'wagmi';
import { getPackageManagerContract, getUSDTContract } from '../contracts';
import { parseUnits } from 'viem';

export const usePackageManager = () => {
  const { address } = useAccount();
  const { writeContractAsync, isPending, isSuccess, error } = useWriteContract();
  
  // Get available packages
  const { data: packageCount } = useReadContract({
    ...getPackageManagerContract(),
    functionName: 'getPackageCount',
  });
  
  // Get package details
  const getPackageDetails = async (packageId) => {
    const contract = getPackageManagerContract();
    return contract.read.getPackage([packageId]);
  };
  
  // Purchase a package
  const purchasePackage = async (packageId, referrerAddress = '0x0000000000000000000000000000000000000000') => {
    // 1. First approve USDT spending
    const packageDetails = await getPackageDetails(packageId);
    const entryAmount = packageDetails.entryUSDT;
    
    // Approve USDT first
    await writeContractAsync({
      ...getUSDTContract(),
      functionName: 'approve',
      args: [getPackageManagerContract().address, entryAmount],
    });
    
    // Then purchase the package
    return writeContractAsync({
      ...getPackageManagerContract(),
      functionName: 'purchase',
      args: [packageId, referrerAddress],
    });
  };
  
  return {
    packageCount: packageCount || 0,
    getPackageDetails,
    purchasePackage,
    isPending,
    isSuccess,
    error
  };
};