import { getContract } from 'viem';
import { publicClient } from '../config/wagmi';
import deployment from './deployment.json';

// Import ABIs
import ShareTokenABI from './abis/ShareToken.json';
import PackageManagerABI from './abis/PackageManager.json';
import VestingVaultABI from './abis/VestingVault.json';
import ReferralManagerABI from './abis/ReferralManager.json';
import USDTTokenABI from './abis/BlockCoopTestTether.json';

// Contract instances
export const getShareTokenContract = () => 
  getContract({
    address: deployment.shareToken as `0x${string}`,
    abi: ShareTokenABI.abi,
    publicClient
  });

export const getPackageManagerContract = () => 
  getContract({
    address: deployment.packageManager as `0x${string}`,
    abi: PackageManagerABI.abi,
    publicClient
  });

export const getVestingVaultContract = () => 
  getContract({
    address: deployment.vestingVault as `0x${string}`,
    abi: VestingVaultABI.abi,
    publicClient
  });

export const getReferralManagerContract = () => 
  getContract({
    address: deployment.referralManager as `0x${string}`,
    abi: ReferralManagerABI.abi,
    publicClient
  });

export const getUSDTContract = () => 
  getContract({
    address: deployment.usdtAddress as `0x${string}`,
    abi: USDTTokenABI.abi,
    publicClient
  });