import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  DollarSign,
  TrendingUp,
  Settings,
  Shield,
  BarChart3,
  Package,
  AlertTriangle
} from 'lucide-react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { AdminStats } from '../types';
import { usePackageManager } from '../hooks/usePackageManager';
import { useWriteContract } from 'wagmi';
import { getPackageManagerContract } from '../contracts';
import { parseUnits, formatUnits } from 'viem';
// @ts-ignore - Temporary ignore for import issue
import { AddPackageModal } from '../components/admin/AddPackageModal';

interface AdminPageProps {
  onNavigate: (page: string) => void;
}

export const AdminPage: React.FC<AdminPageProps> = ({ onNavigate }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'packages' | 'settings'>('overview');
  const { packageCount, getPackageDetails } = usePackageManager();
  const [packages, setPackages] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddPackageForm, setShowAddPackageForm] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const { writeContractAsync, isPending } = useWriteContract();

  // Remove unused onNavigate warning by using it in a comment
  // onNavigate could be used for navigation if needed
  console.log('AdminPage onNavigate available:', typeof onNavigate);

  // Fetch packages from contract
  useEffect(() => {
    const fetchPackages = async () => {
      if (typeof packageCount === 'number' && packageCount > 0) {
        setLoading(true);
        const pkgs = [];
        for (let i = 0; i < packageCount; i++) {
          try {
            const pkg = await getPackageDetails(i);
            pkgs.push({
              id: i,
              name: `Package ${i + 1}`,
              minAmount: Number(formatUnits(pkg.entryUSDT, 18)),
              apy: pkg.priceBps / 100,
              vesting: pkg.vestSplitBps / 100,
              active: pkg.active,
              referralRate: pkg.referralRateBps / 100
            });
          } catch (error) {
            console.error(`Error fetching package ${i}:`, error);
          }
        }
        setPackages(pkgs);
        setLoading(false);
      }
    };

    fetchPackages();
  }, [packageCount, getPackageDetails]);

  // Add new package function
  const addNewPackage = async (packageData: {
    id: string;
    entryAmount: string;
    priceBps: string;
    vestSplitBps: string;
    referralRateBps: string;
  }) => {
    try {
      setActionLoading(true);
      await writeContractAsync({
        ...getPackageManagerContract(),
        functionName: 'addPackage',
        args: [
          BigInt(packageData.id), // Package ID as first parameter
          parseUnits(packageData.entryAmount, 18), // Entry amount in USDT (18 decimals)
          parseInt(packageData.priceBps) * 100, // Convert percentage to basis points
          parseInt(packageData.vestSplitBps) * 100, // Convert percentage to basis points
          parseInt(packageData.referralRateBps) * 100, // Convert percentage to basis points
        ],
      });
      setShowAddPackageForm(false);
      // Refresh packages list
      window.location.reload(); // Simple refresh for now
    } catch (error) {
      console.error('Failed to add package:', error);
      alert('Failed to add package. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  // Toggle package active status
  const togglePackageStatus = async (packageId: number, currentStatus: boolean) => {
    try {
      setActionLoading(true);
      await writeContractAsync({
        ...getPackageManagerContract(),
        functionName: currentStatus ? 'disablePackage' : 'enablePackage',
        args: [BigInt(packageId)],
      });
      // Refresh packages list
      window.location.reload(); // Simple refresh for now
    } catch (error) {
      console.error(`Failed to ${currentStatus ? 'disable' : 'enable'} package:`, error);
      alert(`Failed to ${currentStatus ? 'disable' : 'enable'} package. Please try again.`);
    } finally {
      setActionLoading(false);
    }
  };

  const adminStats: AdminStats = {
    totalUsers: 1247,
    totalValueLocked: 2547890,
    totalTransactions: 12847,
    activePackages: 3,
    pendingWithdrawals: 24500,
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: BarChart3 },
    { id: 'packages', name: 'Packages', icon: Package },
    { id: 'settings', name: 'Settings', icon: Settings },
  ];

  const statCards = [
    {
      title: 'Total Users',
      value: adminStats.totalUsers.toLocaleString(),
      change: '+8.2%',
      icon: Users,
      color: 'primary',
    },
    {
      title: 'Total Value Locked',
      value: `$${(adminStats.totalValueLocked / 1000000).toFixed(1)}M`,
      change: '+15.3%',
      icon: DollarSign,
      color: 'success',
    },
    {
      title: 'Total Transactions',
      value: adminStats.totalTransactions.toLocaleString(),
      change: '+23.1%',
      icon: TrendingUp,
      color: 'primary',
    },
    {
      title: 'Pending Withdrawals',
      value: `$${adminStats.pendingWithdrawals.toLocaleString()}`,
      change: '12 pending',
      icon: AlertTriangle,
      color: 'warning',
    },
  ];

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card hover>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-dark-600 text-sm mb-1">{stat.title}</p>
                  <h3 className="text-2xl font-heading font-bold text-dark-900">
                    {stat.value}
                  </h3>
                  <div className={`text-sm font-medium ${
                    stat.color === 'primary' ? 'text-primary-600' : 
                    stat.color === 'success' ? 'text-success-600' : 'text-orange-600'
                  }`}>
                    {stat.change}
                  </div>
                </div>
                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                  stat.color === 'primary' ? 'bg-primary-100' : 
                  stat.color === 'success' ? 'bg-success-100' : 'bg-orange-100'
                }`}>
                  <stat.icon className={`w-6 h-6 ${
                    stat.color === 'primary' ? 'text-primary-600' : 
                    stat.color === 'success' ? 'text-success-600' : 'text-orange-600'
                  }`} />
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Recent Activity */}
      <Card>
        <h3 className="text-xl font-heading font-semibold text-dark-900 mb-6">
          Recent Platform Activity
        </h3>
        <div className="space-y-4">
          {[
            { user: '0x742d...532', action: 'Purchased Growth Package', amount: '$2,500', time: '2 hours ago' },
            { user: '0x891a...123', action: 'Claimed Rewards', amount: '$150', time: '4 hours ago' },
            { user: '0x456b...789', action: 'Purchased Starter Package', amount: '$500', time: '6 hours ago' },
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-dark-50 rounded-lg">
              <div>
                <h4 className="font-medium text-dark-900">{activity.action}</h4>
                <p className="text-sm text-dark-600">User: {activity.user}</p>
              </div>
              <div className="text-right">
                <div className="font-semibold text-dark-900">{activity.amount}</div>
                <div className="text-sm text-dark-600">{activity.time}</div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  const renderPackages = () => (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-heading font-semibold text-dark-900">
          Investment Packages
        </h3>
        <Button onClick={() => setShowAddPackageForm(true)}>Add New Package</Button>
      </div>

      {loading ? (
        <div className="text-center py-8">Loading packages...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {packages.map((pkg) => (
            <Card key={pkg.id} hover>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-heading font-semibold text-dark-900">
                    {pkg.name}
                  </h4>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    pkg.active ? 'bg-success-100 text-success-700' : 'bg-red-100 text-red-700'
                  }`}>
                    {pkg.active ? 'Active' : 'Inactive'}
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="flex justify-between text-sm">
                    <span className="text-dark-600">Entry Amount:</span>
                    <span className="font-medium">${pkg.minAmount}</span>
                  </p>
                  <p className="flex justify-between text-sm">
                    <span className="text-dark-600">APY:</span>
                    <span className="font-medium">{pkg.apy}%</span>
                  </p>
                  <p className="flex justify-between text-sm">
                    <span className="text-dark-600">Vesting:</span>
                    <span className="font-medium">{pkg.vesting}%</span>
                  </p>
                  <p className="flex justify-between text-sm">
                    <span className="text-dark-600">Referral Rate:</span>
                    <span className="font-medium">{pkg.referralRate}%</span>
                  </p>
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    Edit
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex-1"
                    onClick={() => togglePackageStatus(pkg.id, pkg.active)}
                    loading={actionLoading || isPending}
                    disabled={actionLoading || isPending}
                  >
                    {pkg.active ? 'Disable' : 'Enable'}
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Add Package Modal */}
      <AddPackageModal
        isOpen={showAddPackageForm}
        onClose={() => setShowAddPackageForm(false)}
        onSubmit={addNewPackage}
        loading={actionLoading || isPending}
      />
    </div>
  );

  const renderSettings = () => (
    <div className="space-y-8">
      {/* Platform Settings */}
      <Card>
        <h3 className="text-xl font-heading font-semibold text-dark-900 mb-6">
          Platform Settings
        </h3>
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Platform Fee (%)"
              type="number"
              defaultValue="2.5"
              helper="Fee charged on all transactions"
            />
            <Input
              label="Referral Bonus (%)"
              type="number"
              defaultValue="5"
              helper="Bonus given for referrals"
            />
            <Input
              label="Minimum Investment (USD)"
              type="number"
              defaultValue="100"
              helper="Platform-wide minimum investment"
            />
            <Input
              label="Maximum Investment (USD)"
              type="number"
              defaultValue="100000"
              helper="Platform-wide maximum investment"
            />
          </div>
          <Button>Save Settings</Button>
        </div>
      </Card>

      {/* Security Settings */}
      <Card>
        <div className="flex items-center space-x-3 mb-6">
          <Shield className="w-6 h-6 text-primary-600" />
          <h3 className="text-xl font-heading font-semibold text-dark-900">
            Security Settings
          </h3>
        </div>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-primary-50 rounded-lg">
            <div>
              <h4 className="font-medium text-primary-900">Multi-Signature Wallet</h4>
              <p className="text-sm text-primary-700">Requires 3 of 5 signatures for major operations</p>
            </div>
            <div className="text-sm font-medium text-primary-600">Active</div>
          </div>
          <div className="flex items-center justify-between p-4 bg-success-50 rounded-lg">
            <div>
              <h4 className="font-medium text-success-900">Contract Audit</h4>
              <p className="text-sm text-success-700">Last audited by CertiK on Dec 15, 2023</p>
            </div>
            <div className="text-sm font-medium text-success-600">Verified</div>
          </div>
          <div className="flex items-center justify-between p-4 bg-orange-50 rounded-lg">
            <div>
              <h4 className="font-medium text-orange-900">Emergency Pause</h4>
              <p className="text-sm text-orange-700">Ability to pause platform in case of emergency</p>
            </div>
            <Button variant="outline" size="sm">
              Configure
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-dark-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-heading font-bold text-dark-900 mb-2">
            Admin Dashboard
          </h1>
          <p className="text-dark-600">
            Manage platform settings, packages, and monitor system performance
          </p>
        </motion.div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-dark-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-dark-500 hover:text-dark-700 hover:border-dark-300'
                  }`}
                >
                  <tab.icon className="w-5 h-5" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'packages' && renderPackages()}
          {activeTab === 'settings' && renderSettings()}
        </motion.div>
      </div>
    </div>
  );
};


