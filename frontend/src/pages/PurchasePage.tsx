import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, CheckCircle } from 'lucide-react';
import { Package } from '../types';
import { PackageCard } from '../components/purchase/PackageCard';
import { PaymentForm } from '../components/purchase/PaymentForm';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { useWallet } from '../hooks/useWallet';
import { WalletModal } from '../components/wallet/WalletModal';
import { usePackageManager } from '../hooks/usePackageManager';
import { formatUnits } from 'viem';

interface PurchasePageProps {
  onNavigate: (page: string) => void;
}

export const PurchasePage: React.FC<PurchasePageProps> = ({ onNavigate }) => {
  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null);
  const [showPayment, setShowPayment] = useState(false);
  const [paymentComplete, setPaymentComplete] = useState(false);
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);
  const { isConnected, user } = useWallet();
  const { packageCount, getPackageDetails, purchasePackage, isPending, isSuccess } = usePackageManager();
  const [contractPackages, setContractPackages] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPackages = async () => {
      if (Number(packageCount) > 0) {
        setLoading(true);
        const pkgs = [];
        for (let i = 0; i < Number(packageCount); i++) {
          try {
            const pkg = await getPackageDetails(i);
            if (pkg.active) {
              pkgs.push({
                id: i.toString(),
                name: `Package ${i + 1}`,
                minAmount: Number(formatUnits(pkg.entryUSDT, 18)),
                maxAmount: Number(formatUnits(pkg.entryUSDT, 18)) * 10, // Example max amount
                vestingPeriod: pkg.vestSplitBps / 100, // Convert basis points to percentage
                interestRate: pkg.priceBps / 100, // Convert basis points to percentage
                description: `Investment package with ${pkg.priceBps / 100}% returns`,
                features: [
                  `Entry amount: ${formatUnits(pkg.entryUSDT, 18)} USDT`,
                  `Vesting split: ${pkg.vestSplitBps / 100}%`,
                  `Referral rate: ${pkg.referralRateBps / 100}%`,
                ],
                contractData: pkg
              });
            }
          } catch (error) {
            console.error(`Error fetching package ${i}:`, error);
          }
        }
        setContractPackages(pkgs);
        setLoading(false);
      }
    };

    fetchPackages();
  }, [packageCount, getPackageDetails]);

  // Use contract packages if available, otherwise use mock data
  const packages: Package[] = contractPackages.length > 0 ? contractPackages : [
    {
      id: '1',
      name: 'Starter',
      minAmount: 100,
      maxAmount: 1000,
      vestingPeriod: 3,
      interestRate: 8,
      description: 'Perfect for beginners',
      features: [
        'Quarterly claim rewards',
        'Basic analytics dashboard',
        'Community support',
        'Mobile app access',
      ],
    },
    {
      id: '2',
      name: 'Growth',
      minAmount: 1000,
      maxAmount: 10000,
      vestingPeriod: 6,
      interestRate: 12,
      description: 'Most popular choice',
      features: [
        'Monthly claim rewards',
        'Advanced analytics',
        'Priority support',
        'Referral bonuses',
        'Governance voting rights',
      ],
      popular: true,
    },
    {
      id: '3',
      name: 'Premium',
      minAmount: 10000,
      maxAmount: 100000,
      vestingPeriod: 12,
      interestRate: 18,
      description: 'Maximum returns',
      features: [
        'Weekly claim rewards',
        'Premium analytics suite',
        'Dedicated account manager',
        'Enhanced referral bonuses',
        'Governance voting rights',
        'Early access to new features',
      ],
    },
  ];

  const handlePackageSelect = (packageId: string) => {
    const pkg = packages.find(p => p.id === packageId);
    setSelectedPackage(pkg || null);
  };

  const handleContinue = () => {
    if (!isConnected) {
      setIsWalletModalOpen(true);
      return;
    }
    setShowPayment(true);
  };

  const handlePayment = async (paymentData: any) => {
    try {
      if (selectedPackage && contractPackages.length > 0) {
        // Use real contract interaction
        await purchasePackage(parseInt(selectedPackage.id), paymentData.referralCode || '******************************************');
        setPaymentComplete(true);
      } else {
        // Mock payment for demo
        await new Promise(resolve => setTimeout(resolve, 2000));
        setPaymentComplete(true);
      }
    } catch (error) {
      console.error('Payment failed:', error);
    }
  };

  if (paymentComplete) {
    return (
      <div className="min-h-screen bg-dark-50 py-12">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center"
          >
            <Card>
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-8 h-8 text-success-600" />
              </div>
              <h1 className="text-3xl font-heading font-bold text-dark-900 mb-4">
                Investment Successful!
              </h1>
              <p className="text-dark-600 mb-8">
                Your investment has been processed and tokens will be vested according to your selected package schedule.
              </p>
              <div className="space-y-4">
                <Button onClick={() => onNavigate('dashboard')} className="w-full">
                  View Dashboard
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => onNavigate('home')}
                  className="w-full"
                >
                  Back to Home
                </Button>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => showPayment ? setShowPayment(false) : onNavigate('home')}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {showPayment ? 'Back to Packages' : 'Back to Home'}
          </Button>
          
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <h1 className="text-3xl lg:text-4xl font-heading font-bold text-dark-900 mb-4">
              {showPayment ? 'Complete Your Investment' : 'Choose Your Investment Package'}
            </h1>
            <p className="text-xl text-dark-600">
              {showPayment 
                ? 'Review your selection and complete the payment process'
                : 'Select the package that best fits your investment goals and risk tolerance'
              }
            </p>
          </motion.div>
        </div>

        {showPayment ? (
          /* Payment Form */
          <div className="max-w-2xl mx-auto">
            <PaymentForm
              selectedPackage={selectedPackage}
              onPayment={handlePayment}
            />
          </div>
        ) : (
          /* Package Selection */
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
              {packages.map((pkg, index) => (
                <motion.div
                  key={pkg.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <PackageCard
                    package={pkg}
                    onSelect={handlePackageSelect}
                    selected={selectedPackage?.id === pkg.id}
                  />
                </motion.div>
              ))}
            </div>

            {selectedPackage && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="max-w-2xl mx-auto"
              >
                <Card>
                  <div className="text-center">
                    <h3 className="text-xl font-heading font-semibold text-dark-900 mb-4">
                      Selected: {selectedPackage.name} Package
                    </h3>
                    <p className="text-dark-600 mb-6">
                      You've selected the {selectedPackage.name} package with {selectedPackage.interestRate}% APY 
                      and {selectedPackage.vestingPeriod} months vesting period.
                    </p>
                    <Button onClick={handleContinue} size="lg" className="w-full sm:w-auto">
                      {isConnected ? 'Continue to Payment' : 'Connect Wallet to Continue'}
                    </Button>
                  </div>
                </Card>
              </motion.div>
            )}
          </>
        )}
      </div>

      <WalletModal
        isOpen={isWalletModalOpen}
        onClose={() => setIsWalletModalOpen(false)}
      />
    </div>
  );
};
